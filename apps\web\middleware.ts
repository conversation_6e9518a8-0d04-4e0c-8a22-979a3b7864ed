import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

/**
 * REAL-TIME AUTH MIDDLEWARE - Validates role changes immediately
 * Performs server validation on every request to detect role changes
 * Maintains 30-day sessions while ensuring real-time security
 */
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  console.log('🔍 MIDDLEWARE: Processing request for:', pathname);

  // ALWAYS allow access to signin page - NO EXCEPTIONS
  if (pathname === '/signin') {
    console.log('✅ MIDDLEWARE: Allowing access to /signin page');
    return NextResponse.next();
  }

  // Allow access to auth-related pages
  if (pathname.startsWith('/(auth)') || pathname.includes('/signin')) {
    console.log('✅ MIDDLEWARE: Allowing access to auth page:', pathname);
    return NextResponse.next();
  }

  // For all protected routes, check cookie AND validate with server
  const payloadToken = request.cookies.get('payload-token');

  if (!payloadToken || !payloadToken.value) {
    console.log('❌ MIDDLEWARE: No auth cookie found, redirecting to signin');
    console.log('Available cookies:', request.cookies.getAll().map(c => c.name));

    // Prevent redirect loops by checking if we're already redirecting
    if (request.nextUrl.searchParams.get('redirected') === 'true') {
      console.log('⚠️ MIDDLEWARE: Redirect loop detected, allowing access');
      return NextResponse.next();
    }

    const redirectUrl = new URL('/signin', request.url);
    redirectUrl.searchParams.set('redirected', 'true');
    redirectUrl.searchParams.set('from', pathname);
    return NextResponse.redirect(redirectUrl);
  }

  // 🚨 CRITICAL: REAL-TIME SERVER VALIDATION FOR ROLE CHANGES
  try {
    const apiUrl = 'https://grandline-cms.vercel.app/api';
    const response = await fetch(`${apiUrl}/users/me`, {
      headers: {
        'Cookie': `payload-token=${payloadToken.value}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const userData = await response.json();
      const user = userData.user || userData;

      // 🚨 CRITICAL: Check if user role is still "trainee"
      if (user && user.role !== 'trainee') {
        console.log('🚨 MIDDLEWARE SECURITY: User role changed from trainee to', user.role, '- BLOCKING ACCESS');

        // Clear the invalid cookie and redirect to signin
        const redirectResponse = NextResponse.redirect(new URL('/signin', request.url));
        redirectResponse.cookies.delete('payload-token');
        return redirectResponse;
      }

      // 🚨 CRITICAL: Check if user account is still active
      if (user && !user.isActive) {
        console.log('🚨 MIDDLEWARE SECURITY: User account deactivated - BLOCKING ACCESS');

        // Clear the invalid cookie and redirect to signin
        const redirectResponse = NextResponse.redirect(new URL('/signin', request.url));
        redirectResponse.cookies.delete('payload-token');
        return redirectResponse;
      }

      console.log('✅ MIDDLEWARE: Server validation passed - role:', user.role, 'active:', user.isActive);
      return NextResponse.next();
    } else {
      console.log('❌ MIDDLEWARE: Server validation failed with status:', response.status);

      // Clear invalid cookie and redirect to signin
      const redirectResponse = NextResponse.redirect(new URL('/signin', request.url));
      redirectResponse.cookies.delete('payload-token');
      return redirectResponse;
    }
  } catch (error) {
    console.error('❌ MIDDLEWARE: Error validating token:', error);

    // On error, allow access but log the issue (fallback to client-side validation)
    console.log('⚠️ MIDDLEWARE: Falling back to client-side validation');
    return NextResponse.next();
  }
}

export const config = {
  // CRITICAL: Always exclude /signin to prevent redirect loops
  // Also exclude Next.js internal routes and static assets
  matcher: ['/', '/((?!signin|api|_next/static|_next/image|favicon.ico|.*\\..*).*)'],
}
