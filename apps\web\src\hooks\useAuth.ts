/**
 * PERSISTENT Authentication Hook - TRUE 30-DAY SESSIONS!
 *
 * This hook provides true persistent authentication like Facebook/LinkedIn
 * by relying on client-side cookie validation instead of server calls.
 * Server validation only happens when explicitly needed.
 *
 * REPLACES the old server-validation hook to fix 1-2 hour logout issue.
 */

import { useState, useEffect } from 'react';
import { AuthCookies } from '@/utils/auth-cookies';

export interface AuthUser {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  username?: string;
  role: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthState {
  user: AuthUser | null;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  securityAlert: {
    show: boolean;
    type: 'role-changed' | 'account-deactivated' | 'session-expired';
    message: string;
  } | null;
}

/**
 * Persistent authentication hook that doesn't validate on every page load
 * Only validates when explicitly requested or when authentication fails
 */
export function useAuth(): AuthState & {
  validateWithServer: () => Promise<boolean>;
  logout: () => void;
  recoverSession: () => boolean;
} {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [securityAlert, setSecurityAlert] = useState<AuthState['securityAlert']>(null);

  // Initialize authentication state - FETCH REAL USER DATA
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        console.log('🔍 PERSISTENT AUTH: Initializing authentication...');

        // Check if we have a valid cookie
        const isAuthenticated = AuthCookies.isAuthenticated();

        if (isAuthenticated) {
          console.log('✅ PERSISTENT AUTH: Valid cookie found, fetching user data...');

          // First try to get cached user data from localStorage
          const cachedUserData = localStorage.getItem('auth_user_data');
          if (cachedUserData) {
            try {
              const userData = JSON.parse(cachedUserData);
              console.log('✅ PERSISTENT AUTH: Using cached user data');
              setUser(userData);
              setError(null);
              setLoading(false);
              return;
            } catch (e) {
              console.log('⚠️ PERSISTENT AUTH: Invalid cached data, fetching from server');
            }
          }

          // If no cached data, fetch from server
          await fetchUserFromServer();
        } else {
          console.log('❌ PERSISTENT AUTH: No valid cookie found');

          // Try session recovery
          const recovered = AuthCookies.recoverSession();
          if (recovered) {
            console.log('✅ PERSISTENT AUTH: Session recovered, fetching user data...');
            await fetchUserFromServer();
          } else {
            console.log('❌ PERSISTENT AUTH: No session to recover');
            setUser(null);
            setError('No authentication found');
            setLoading(false);
          }
        }
      } catch (err) {
        console.error('❌ PERSISTENT AUTH: Initialization error:', err);
        setError('Authentication initialization failed');
        setUser(null);
        setLoading(false);
      }
    };

    const fetchUserFromServer = async () => {
      try {
        const token = AuthCookies.getToken();
        if (!token) {
          setUser(null);
          setError('No authentication token');
          setLoading(false);
          return;
        }

        const apiUrl = 'https://grandline-cms.vercel.app/api';
        const response = await fetch(`${apiUrl}/users/me`, {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          const userData = await response.json();
          console.log('✅ PERSISTENT AUTH: Real user data fetched successfully');

          // Extract user data from PayloadCMS response
          const extractedUser = userData.user || userData;
          if (extractedUser) {
            // Cache the user data for future use
            localStorage.setItem('auth_user_data', JSON.stringify(extractedUser));

            setUser(extractedUser);
            setError(null);
          } else {
            setError('Unable to extract user data');
            setUser(null);
          }
        } else {
          console.log('❌ PERSISTENT AUTH: Failed to fetch user data:', response.status);

          // Only clear authentication if it's a definitive auth failure
          if (response.status === 401 || response.status === 403) {
            console.log('🚨 PERSISTENT AUTH: Authentication invalid, clearing session');
            AuthCookies.logout();
            localStorage.removeItem('auth_user_data');
            setUser(null);
            setError('Authentication expired');
          } else {
            // For other errors, keep the session but show error
            setError(`Failed to fetch user data: ${response.status}`);
          }
        }
      } catch (err) {
        console.error('❌ PERSISTENT AUTH: Error fetching user data:', err);
        setError('Network error while fetching user data');
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  /**
   * Validate authentication with server (only when explicitly called)
   */
  const validateWithServer = async (): Promise<boolean> => {
    try {
      console.log('🌐 PERSISTENT AUTH: Validating with server...');

      const token = AuthCookies.getToken();
      if (!token) {
        console.log('❌ PERSISTENT AUTH: No token for server validation');
        return false;
      }

      const apiUrl = 'https://grandline-cms.vercel.app/api';
      const response = await fetch(`${apiUrl}/users/me`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const userData = await response.json();
        console.log('✅ PERSISTENT AUTH: Server validation successful');

        // Update user data with server response
        const extractedUser = userData.user || userData;
        if (extractedUser) {
          // Cache the updated user data
          localStorage.setItem('auth_user_data', JSON.stringify(extractedUser));

          setUser(extractedUser);
          setError(null);
          return true;
        }
      } else {
        console.log('❌ PERSISTENT AUTH: Server validation failed:', response.status);

        // Only clear authentication if it's a definitive auth failure
        if (response.status === 401 || response.status === 403) {
          console.log('🚨 PERSISTENT AUTH: Authentication invalid, clearing session');
          AuthCookies.logout();
          setUser(null);
          setError('Authentication expired');
          return false;
        }
      }

      return false;
    } catch (err) {
      console.error('❌ PERSISTENT AUTH: Server validation error:', err);
      // Don't clear authentication on network errors
      return false;
    }
  };

  /**
   * Logout and clear all authentication data
   */
  const logout = () => {
    console.log('🚪 PERSISTENT AUTH: Logging out...');
    AuthCookies.logout();

    // Clear cached user data
    localStorage.removeItem('auth_user_data');

    setUser(null);
    setError(null);
    setSecurityAlert(null);
  };

  /**
   * Attempt session recovery
   */
  const recoverSession = (): boolean => {
    console.log('🔄 PERSISTENT AUTH: Attempting session recovery...');
    const recovered = AuthCookies.recoverSession();

    if (recovered) {
      console.log('✅ PERSISTENT AUTH: Session recovered, fetching real user data...');

      // Try to get cached user data first
      const cachedUserData = localStorage.getItem('auth_user_data');
      if (cachedUserData) {
        try {
          const userData = JSON.parse(cachedUserData);
          setUser(userData);
          setError(null);
          return true;
        } catch (e) {
          console.log('⚠️ PERSISTENT AUTH: Invalid cached data during recovery');
        }
      }

      // If no cached data, trigger a server fetch
      validateWithServer();
    }

    return recovered;
  };

  return {
    user,
    loading,
    error,
    isAuthenticated: !!user && AuthCookies.isAuthenticated(),
    securityAlert,
    validateWithServer,
    logout,
    recoverSession
  };
}

/**
 * Get user's full name
 */
export function getFullName(user: AuthUser | null): string {
  if (!user) return 'User';

  const firstName = user.firstName || '';
  const lastName = user.lastName || '';

  if (firstName && lastName) {
    return `${firstName} ${lastName}`;
  } else if (firstName) {
    return firstName;
  } else if (lastName) {
    return lastName;
  } else {
    return user.email?.split('@')[0] || 'User';
  }
}

/**
 * Get user's initials
 */
export function getUserInitials(user: AuthUser | null): string {
  if (!user) return 'U';

  const firstName = user.firstName || '';
  const lastName = user.lastName || '';

  if (firstName && lastName) {
    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
  } else if (firstName) {
    return firstName.charAt(0).toUpperCase();
  } else if (lastName) {
    return lastName.charAt(0).toUpperCase();
  } else {
    return user.email?.charAt(0).toUpperCase() || 'U';
  }
}
