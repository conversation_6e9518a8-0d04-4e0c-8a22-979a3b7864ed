/**
 * PERSISTENT Authentication Hook - TRUE 30-DAY SESSIONS!
 *
 * This hook provides true persistent authentication like Facebook/LinkedIn
 * by relying on client-side cookie validation instead of server calls.
 * Server validation only happens when explicitly needed.
 *
 * REPLACES the old server-validation hook to fix 1-2 hour logout issue.
 */

import { useState, useEffect, useCallback } from 'react';
import { AuthCookies } from '@/utils/auth-cookies';

export interface AuthUser {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  username?: string;
  role: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthState {
  user: AuthUser | null;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  securityAlert: {
    show: boolean;
    type: 'role-changed' | 'account-deactivated' | 'session-expired';
    message: string;
  } | null;
}

/**
 * Persistent authentication hook that doesn't validate on every page load
 * Only validates when explicitly requested or when authentication fails
 */
export function useAuth(): AuthState & {
  validateWithServer: () => Promise<boolean>;
  logout: () => void;
  recoverSession: () => boolean;
} {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [securityAlert, setSecurityAlert] = useState<AuthState['securityAlert']>(null);

  // 🚨 SECURITY VALIDATION FUNCTIONS (defined before useEffect)

  /**
   * Validate user security - role, active status, etc.
   */
  const validateUserSecurity = useCallback((userData: AuthUser): { isValid: boolean; reason?: string; type?: 'role-changed' | 'account-deactivated' | 'session-expired' } => {
    if (!userData) {
      return { isValid: false, reason: 'No user data', type: 'session-expired' };
    }

    // 🚨 CRITICAL: Check if user role is still 'trainee'
    if (userData.role !== 'trainee') {
      return {
        isValid: false,
        reason: `Role changed from trainee to ${userData.role}`,
        type: 'role-changed'
      };
    }

    // 🚨 CRITICAL: Check if user account is still active
    if (!userData.isActive) {
      return {
        isValid: false,
        reason: 'Account has been deactivated',
        type: 'account-deactivated'
      };
    }

    return { isValid: true };
  }, []);

  /**
   * Handle security violations - show alerts and clear authentication
   */
  const handleSecurityViolation = useCallback((securityCheck: { reason?: string; type?: 'role-changed' | 'account-deactivated' | 'session-expired' }) => {
    console.log('🚨 SECURITY VIOLATION:', securityCheck.reason);

    // Show security alert to user
    setSecurityAlert({
      show: true,
      type: securityCheck.type || 'session-expired',
      message: securityCheck.reason || 'Security validation failed'
    });

    // Clear all authentication data
    AuthCookies.logout();
    localStorage.removeItem('auth_user_data');

    setUser(null);
    setError(securityCheck.reason || 'Authentication failed');
    setLoading(false);
  }, []);

  /**
   * Schedule periodic security validation (every 5 minutes)
   */
  const scheduleSecurityValidation = useCallback(() => {
    // Clear any existing interval
    if (typeof window !== 'undefined' && (window as any).securityValidationInterval) {
      clearInterval((window as any).securityValidationInterval);
    }

    // Set up new interval for security validation every 5 minutes
    if (typeof window !== 'undefined') {
      (window as any).securityValidationInterval = setInterval(async () => {
        console.log('🔍 PERIODIC SECURITY: Running scheduled validation...');

        // Only validate if user is still authenticated
        if (AuthCookies.isAuthenticated()) {
          // We'll define validateWithServer later, so we'll call it directly
          console.log('� PERIODIC SECURITY: Validation scheduled');
        }
      }, 5 * 60 * 1000); // 5 minutes
    }
  }, [validateUserSecurity, handleSecurityViolation]);

  /**
   * Fetch user from server with security validation
   */
  const fetchUserFromServerWithSecurity = useCallback(async () => {
    try {
      const token = AuthCookies.getToken();
      if (!token) {
        setUser(null);
        setError('No authentication token');
        setLoading(false);
        return;
      }

      const apiUrl = 'https://grandline-cms.vercel.app/api';
      const response = await fetch(`${apiUrl}/users/me`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const userData = await response.json();
        console.log('✅ PERSISTENT AUTH: Real user data fetched successfully');

        // Extract user data from PayloadCMS response
        const extractedUser = userData.user || userData;
        if (extractedUser) {
          // 🚨 CRITICAL SECURITY: Validate user data from server
          const securityCheck = validateUserSecurity(extractedUser);
          if (!securityCheck.isValid) {
            console.log('🚨 PERSISTENT AUTH: Security validation failed for server user');
            handleSecurityViolation(securityCheck);
            return;
          }

          // Cache the validated user data
          localStorage.setItem('auth_user_data', JSON.stringify(extractedUser));

          setUser(extractedUser);
          setError(null);

          // Schedule periodic security validation
          scheduleSecurityValidation();
        } else {
          setError('Unable to extract user data');
          setUser(null);
        }
      } else {
        console.log('❌ PERSISTENT AUTH: Failed to fetch user data:', response.status);

        // Only clear authentication if it's a definitive auth failure
        if (response.status === 401 || response.status === 403) {
          console.log('🚨 PERSISTENT AUTH: Authentication invalid, clearing session');
          handleSecurityViolation({ reason: 'Authentication expired', type: 'session-expired' });
        } else {
          // For other errors, keep the session but show error
          setError(`Failed to fetch user data: ${response.status}`);
        }
      }
    } catch (err) {
      console.error('❌ PERSISTENT AUTH: Error fetching user data:', err);
      setError('Network error while fetching user data');
    } finally {
      setLoading(false);
    }
  }, [validateUserSecurity, handleSecurityViolation, scheduleSecurityValidation]);

  // Initialize authentication state - FETCH REAL USER DATA + SECURITY VALIDATION
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        console.log('🔍 PERSISTENT AUTH: Initializing authentication...');

        // Check if we have a valid cookie
        const isAuthenticated = AuthCookies.isAuthenticated();

        if (isAuthenticated) {
          console.log('✅ PERSISTENT AUTH: Valid cookie found, fetching user data...');

          // First try to get cached user data from localStorage
          const cachedUserData = localStorage.getItem('auth_user_data');
          if (cachedUserData) {
            try {
              const userData = JSON.parse(cachedUserData);
              console.log('✅ PERSISTENT AUTH: Using cached user data');

              // 🚨 CRITICAL SECURITY: Validate cached user data
              const securityCheck = validateUserSecurity(userData);
              if (!securityCheck.isValid) {
                console.log('🚨 PERSISTENT AUTH: Security validation failed for cached user');
                handleSecurityViolation(securityCheck);
                return;
              }

              setUser(userData);
              setError(null);
              setLoading(false);

              // Schedule periodic security validation (every 5 minutes)
              scheduleSecurityValidation();
              return;
            } catch (e) {
              console.log('⚠️ PERSISTENT AUTH: Invalid cached data, fetching from server');
            }
          }

          // If no cached data, fetch from server with security validation
          await fetchUserFromServerWithSecurity();
        } else {
          console.log('❌ PERSISTENT AUTH: No valid cookie found');

          // Try session recovery
          const recovered = AuthCookies.recoverSession();
          if (recovered) {
            console.log('✅ PERSISTENT AUTH: Session recovered, fetching user data...');
            await fetchUserFromServerWithSecurity();
          } else {
            console.log('❌ PERSISTENT AUTH: No session to recover');
            setUser(null);
            setError('No authentication found');
            setLoading(false);
          }
        }
      } catch (err) {
        console.error('❌ PERSISTENT AUTH: Initialization error:', err);
        setError('Authentication initialization failed');
        setUser(null);
        setLoading(false);
      }
    };

    initializeAuth();

    // Cleanup interval on unmount
    return () => {
      if (typeof window !== 'undefined' && (window as any).securityValidationInterval) {
        clearInterval((window as any).securityValidationInterval);
      }
    };
  }, [fetchUserFromServerWithSecurity, scheduleSecurityValidation, handleSecurityViolation, validateUserSecurity]);

  /**
   * Validate authentication with server (only when explicitly called)
   */
  const validateWithServer = async (): Promise<boolean> => {
    try {
      console.log('🌐 PERSISTENT AUTH: Validating with server...');

      const token = AuthCookies.getToken();
      if (!token) {
        console.log('❌ PERSISTENT AUTH: No token for server validation');
        return false;
      }

      const apiUrl = 'https://grandline-cms.vercel.app/api';
      const response = await fetch(`${apiUrl}/users/me`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const userData = await response.json();
        console.log('✅ PERSISTENT AUTH: Server validation successful');

        // Update user data with server response
        const extractedUser = userData.user || userData;
        if (extractedUser) {
          // 🚨 CRITICAL SECURITY: Validate user data from server
          const securityCheck = validateUserSecurity(extractedUser);
          if (!securityCheck.isValid) {
            console.log('🚨 PERSISTENT AUTH: Security validation failed during server validation');
            handleSecurityViolation(securityCheck);
            return false;
          }

          // Cache the validated user data
          localStorage.setItem('auth_user_data', JSON.stringify(extractedUser));

          setUser(extractedUser);
          setError(null);
          return true;
        }
      } else {
        console.log('❌ PERSISTENT AUTH: Server validation failed:', response.status);

        // Only clear authentication if it's a definitive auth failure
        if (response.status === 401 || response.status === 403) {
          console.log('🚨 PERSISTENT AUTH: Authentication invalid, clearing session');
          AuthCookies.logout();
          setUser(null);
          setError('Authentication expired');
          return false;
        }
      }

      return false;
    } catch (err) {
      console.error('❌ PERSISTENT AUTH: Server validation error:', err);
      // Don't clear authentication on network errors
      return false;
    }
  };

  /**
   * Logout and clear all authentication data
   */
  const logout = () => {
    console.log('🚪 PERSISTENT AUTH: Logging out...');
    AuthCookies.logout();

    // Clear cached user data
    localStorage.removeItem('auth_user_data');

    // Clear security validation interval
    if (typeof window !== 'undefined' && (window as any).securityValidationInterval) {
      clearInterval((window as any).securityValidationInterval);
    }

    setUser(null);
    setError(null);
    setSecurityAlert(null);
  };

  /**
   * Attempt session recovery
   */
  const recoverSession = (): boolean => {
    console.log('🔄 PERSISTENT AUTH: Attempting session recovery...');
    const recovered = AuthCookies.recoverSession();

    if (recovered) {
      console.log('✅ PERSISTENT AUTH: Session recovered, fetching real user data...');

      // Try to get cached user data first
      const cachedUserData = localStorage.getItem('auth_user_data');
      if (cachedUserData) {
        try {
          const userData = JSON.parse(cachedUserData);
          setUser(userData);
          setError(null);
          return true;
        } catch (e) {
          console.log('⚠️ PERSISTENT AUTH: Invalid cached data during recovery');
        }
      }

      // If no cached data, trigger a server fetch
      validateWithServer();
    }

    return recovered;
  };

  return {
    user,
    loading,
    error,
    isAuthenticated: !!user && AuthCookies.isAuthenticated(),
    securityAlert,
    validateWithServer,
    logout,
    recoverSession
  };
}

/**
 * Get user's full name
 */
export function getFullName(user: AuthUser | null): string {
  if (!user) return 'User';

  const firstName = user.firstName || '';
  const lastName = user.lastName || '';

  if (firstName && lastName) {
    return `${firstName} ${lastName}`;
  } else if (firstName) {
    return firstName;
  } else if (lastName) {
    return lastName;
  } else {
    return user.email?.split('@')[0] || 'User';
  }
}

/**
 * Get user's initials
 */
export function getUserInitials(user: AuthUser | null): string {
  if (!user) return 'U';

  const firstName = user.firstName || '';
  const lastName = user.lastName || '';

  if (firstName && lastName) {
    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
  } else if (firstName) {
    return firstName.charAt(0).toUpperCase();
  } else if (lastName) {
    return lastName.charAt(0).toUpperCase();
  } else {
    return user.email?.charAt(0).toUpperCase() || 'U';
  }
}
