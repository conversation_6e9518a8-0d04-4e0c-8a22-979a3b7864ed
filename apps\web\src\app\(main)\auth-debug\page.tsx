'use client';

import React, { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { AuthCookies } from '@/utils/auth-cookies';

/**
 * Authentication Debug Page
 * Test and monitor persistent authentication functionality
 */
export default function AuthDebugPage() {
  const {
    user,
    loading,
    error,
    isAuthenticated,
    validateWithServer,
    logout,
    recoverSession
  } = useAuth();

  const sessionInfo = AuthCookies.getSessionInfo();

  const [serverValidationResult, setServerValidationResult] = useState<string | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  const handleServerValidation = async () => {
    setIsValidating(true);
    setServerValidationResult(null);
    
    try {
      const isValid = await validateWithServer();
      setServerValidationResult(isValid ? 'Server validation successful' : 'Server validation failed');
    } catch (err) {
      setServerValidationResult(`Server validation error: ${err}`);
    } finally {
      setIsValidating(false);
    }
  };

  const handleRecoverSession = () => {
    const recovered = recoverSession();
    alert(recovered ? 'Session recovered successfully' : 'No session to recover');
  };

  const handleLogout = () => {
    logout();
    alert('Logged out successfully');
  };

  const clearAllCookies = () => {
    document.cookie.split(";").forEach(function(c) {
      document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
    });
    alert('All cookies cleared');
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            🔐 Persistent Authentication Debug
          </h1>

          {/* Authentication Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-blue-50 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-blue-900 mb-4">Authentication Status</h2>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="font-medium">Loading:</span>
                  <span className={loading ? 'text-yellow-600' : 'text-green-600'}>
                    {loading ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Authenticated:</span>
                  <span className={isAuthenticated ? 'text-green-600' : 'text-red-600'}>
                    {isAuthenticated ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Has User:</span>
                  <span className={user ? 'text-green-600' : 'text-red-600'}>
                    {user ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Error:</span>
                  <span className={error ? 'text-red-600' : 'text-green-600'}>
                    {error || 'None'}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-green-50 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-green-900 mb-4">Session Info</h2>
              {sessionInfo ? (
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="font-medium">Cookie Auth:</span>
                    <span className={sessionInfo.isAuthenticated ? 'text-green-600' : 'text-red-600'}>
                      {sessionInfo.isAuthenticated ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Has Backup:</span>
                    <span className={sessionInfo.hasBackup ? 'text-green-600' : 'text-red-600'}>
                      {sessionInfo.hasBackup ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Backup Expiry:</span>
                    <span className="text-gray-600 text-sm">
                      {sessionInfo.backupExpiry ? new Date(sessionInfo.backupExpiry).toLocaleString() : 'None'}
                    </span>
                  </div>
                </div>
              ) : (
                <p className="text-gray-600">No session info available</p>
              )}
            </div>
          </div>

          {/* User Information */}
          {user && (
            <div className="bg-purple-50 rounded-lg p-6 mb-8">
              <h2 className="text-xl font-semibold text-purple-900 mb-4">User Information</h2>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="font-medium">ID:</span> {user.id}
                </div>
                <div>
                  <span className="font-medium">Email:</span> {user.email}
                </div>
                <div>
                  <span className="font-medium">Name:</span> {user.firstName} {user.lastName}
                </div>
                <div>
                  <span className="font-medium">Role:</span> {user.role}
                </div>
                <div>
                  <span className="font-medium">Active:</span> 
                  <span className={user.isActive ? 'text-green-600' : 'text-red-600'}>
                    {user.isActive ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Actions</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <button
                onClick={handleServerValidation}
                disabled={isValidating}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {isValidating ? 'Validating...' : 'Validate with Server'}
              </button>
              
              <button
                onClick={handleRecoverSession}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
              >
                Recover Session
              </button>
              
              <button
                onClick={handleLogout}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
              >
                Logout
              </button>
              
              <button
                onClick={clearAllCookies}
                className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
              >
                Clear All Cookies
              </button>
            </div>
          </div>

          {/* Server Validation Result */}
          {serverValidationResult && (
            <div className="bg-yellow-50 rounded-lg p-6 mb-8">
              <h2 className="text-xl font-semibold text-yellow-900 mb-4">Server Validation Result</h2>
              <p className="text-gray-700">{serverValidationResult}</p>
            </div>
          )}

          {/* Cookie Information */}
          <div className="bg-orange-50 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-orange-900 mb-4">Cookie Information</h2>
            <div className="space-y-2">
              <div>
                <span className="font-medium">All Cookies:</span>
                <pre className="bg-white p-2 rounded text-sm mt-2 overflow-x-auto">
                  {document.cookie || 'No cookies found'}
                </pre>
              </div>
              <div>
                <span className="font-medium">Payload Token:</span>
                <pre className="bg-white p-2 rounded text-sm mt-2 overflow-x-auto">
                  {AuthCookies.getToken() || 'No payload token found'}
                </pre>
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 rounded-lg p-6 mt-8">
            <h2 className="text-xl font-semibold text-blue-900 mb-4">Testing Instructions</h2>
            <ol className="list-decimal list-inside space-y-2 text-gray-700">
              <li>Login to the application normally</li>
              <li>Visit this debug page to see your authentication status</li>
              <li>Close your browser completely and reopen it</li>
              <li>Navigate back to this page - you should still be authenticated</li>
              <li>The session should persist for 30 days without server validation</li>
              <li>Use "Validate with Server" only when you need to check server-side status</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}
